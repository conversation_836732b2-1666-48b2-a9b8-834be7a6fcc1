\headcommand {\slideentry {0}{0}{1}{1/1}{}{0}}
\headcommand {\beamer@framepages {1}{1}}
\headcommand {\slideentry {0}{0}{2}{2/2}{}{0}}
\headcommand {\beamer@framepages {2}{2}}
\headcommand {\beamer@sectionpages {1}{2}}
\headcommand {\beamer@subsectionpages {1}{2}}
\headcommand {\sectionentry {1}{Background}{3}{Background}{0}}
\headcommand {\slideentry {1}{0}{1}{3/3}{}{0}}
\headcommand {\beamer@framepages {3}{3}}
\headcommand {\beamer@subsectionpages {3}{3}}
\headcommand {\beamer@subsectionentry {0}{1}{1}{4}{Decision Trees}}
\headcommand {\slideentry {1}{1}{1}{4/4}{Decision Trees}{0}}
\headcommand {\beamer@framepages {4}{4}}
\headcommand {\slideentry {1}{1}{2}{5/5}{Decision Trees}{0}}
\headcommand {\beamer@framepages {5}{5}}
\headcommand {\beamer@subsectionpages {4}{5}}
\headcommand {\beamer@subsectionentry {0}{1}{2}{6}{Counterfactual explanations}}
\headcommand {\slideentry {1}{2}{1}{6/6}{Counterfactual explanations}{0}}
\headcommand {\beamer@framepages {6}{6}}
\headcommand {\slideentry {1}{2}{2}{7/7}{Counterfactual explanations}{0}}
\headcommand {\beamer@framepages {7}{7}}
\headcommand {\beamer@subsectionpages {6}{7}}
\headcommand {\beamer@subsectionentry {0}{1}{3}{8}{Model extraction attacks}}
\headcommand {\slideentry {1}{3}{1}{8/11}{Model extraction attacks}{0}}
\headcommand {\beamer@framepages {8}{11}}
\headcommand {\slideentry {1}{3}{2}{12/13}{Model extraction attacks}{0}}
\headcommand {\beamer@framepages {12}{13}}
\headcommand {\slideentry {1}{3}{3}{14/15}{Model extraction attacks}{0}}
\headcommand {\beamer@framepages {14}{15}}
\headcommand {\beamer@sectionpages {3}{15}}
\headcommand {\beamer@subsectionpages {8}{15}}
\headcommand {\sectionentry {2}{Project 1 --- From Counterfactuals to Trees: Competitive Analysis of Model Extraction Attacks}{16}{Project 1 --- From Counterfactuals to Trees: Competitive Analysis of Model Extraction Attacks}{0}}
\headcommand {\slideentry {2}{0}{1}{16/16}{}{0}}
\headcommand {\beamer@framepages {16}{16}}
\headcommand {\beamer@subsectionpages {16}{16}}
\headcommand {\beamer@subsectionentry {0}{2}{1}{17}{Related Work}}
\headcommand {\beamer@subsubsectionentry {0}{2}{1}{1}{17}{Surrogate attacks}}
\headcommand {\slideentry {2}{1}{1}{17/19}{Related Work}{0}}
\headcommand {\beamer@framepages {17}{19}}
\headcommand {\beamer@subsubsectionentry {0}{2}{1}{2}{20}{Steal-ML}}
\headcommand {\slideentry {2}{1}{2}{20/20}{Related Work}{0}}
\headcommand {\beamer@framepages {20}{20}}
\headcommand {\beamer@subsectionpages {17}{20}}
\headcommand {\beamer@subsectionentry {0}{2}{2}{21}{Tree Reconstruction Attack (TRA)}}
\headcommand {\slideentry {2}{2}{1}{21/27}{Tree Reconstruction Attack (TRA)}{0}}
\headcommand {\beamer@framepages {21}{27}}
\headcommand {\slideentry {2}{2}{2}{28/29}{Tree Reconstruction Attack (TRA)}{0}}
\headcommand {\beamer@framepages {28}{29}}
\headcommand {\beamer@sectionpages {16}{29}}
\headcommand {\beamer@subsectionpages {21}{29}}
\headcommand {\sectionentry {3}{Results}{30}{Results}{0}}
\headcommand {\beamer@subsectionpages {30}{29}}
\headcommand {\beamer@subsectionentry {0}{3}{1}{30}{Theoretical results}}
\headcommand {\slideentry {3}{1}{1}{30/31}{Theoretical results}{0}}
\headcommand {\beamer@framepages {30}{31}}
\headcommand {\slideentry {3}{1}{2}{32/34}{Theoretical results}{0}}
\headcommand {\beamer@framepages {32}{34}}
\headcommand {\beamer@subsectionpages {30}{34}}
\headcommand {\beamer@subsectionentry {0}{3}{2}{35}{Experimental results}}
\headcommand {\slideentry {3}{2}{1}{35/35}{Experimental results}{0}}
\headcommand {\beamer@framepages {35}{35}}
\headcommand {\slideentry {3}{2}{2}{36/36}{Experimental results}{0}}
\headcommand {\beamer@framepages {36}{36}}
\headcommand {\slideentry {3}{2}{3}{37/37}{Experimental results}{0}}
\headcommand {\beamer@framepages {37}{37}}
\headcommand {\slideentry {3}{2}{4}{38/38}{Experimental results}{0}}
\headcommand {\beamer@framepages {38}{38}}
\headcommand {\beamer@sectionpages {30}{38}}
\headcommand {\beamer@subsectionpages {35}{38}}
\headcommand {\sectionentry {4}{Project 2 --- Learning Voronoi Diagrams for Optimal Counterfactual Explanations in Tree Ensembles}{39}{Project 2 --- Learning Voronoi Diagrams for Optimal Counterfactual Explanations in Tree Ensembles}{0}}
\headcommand {\slideentry {4}{0}{1}{39/39}{}{0}}
\headcommand {\beamer@framepages {39}{39}}
\headcommand {\slideentry {4}{0}{2}{40/40}{}{0}}
\headcommand {\beamer@framepages {40}{40}}
\headcommand {\slideentry {4}{0}{3}{41/41}{}{0}}
\headcommand {\beamer@framepages {41}{41}}
\headcommand {\slideentry {4}{0}{4}{42/42}{}{0}}
\headcommand {\beamer@framepages {42}{42}}
\headcommand {\slideentry {4}{0}{5}{43/43}{}{0}}
\headcommand {\beamer@framepages {43}{43}}
\headcommand {\beamer@sectionpages {39}{43}}
\headcommand {\beamer@subsectionpages {39}{43}}
\headcommand {\sectionentry {5}{Project 3 --- Guaranteeing Local Optimality in Model-Agnostic Counterfactuals}{44}{Project 3 --- Guaranteeing Local Optimality in Model-Agnostic Counterfactuals}{0}}
\headcommand {\slideentry {5}{0}{1}{44/44}{}{0}}
\headcommand {\beamer@framepages {44}{44}}
\headcommand {\slideentry {5}{0}{2}{45/45}{}{0}}
\headcommand {\beamer@framepages {45}{45}}
\headcommand {\slideentry {5}{0}{3}{46/46}{}{0}}
\headcommand {\beamer@framepages {46}{46}}
\headcommand {\beamer@sectionpages {44}{46}}
\headcommand {\beamer@subsectionpages {44}{46}}
\headcommand {\sectionentry {6}{Conclusion and Future works}{47}{Conclusion and Future works}{0}}
\headcommand {\slideentry {6}{0}{1}{47/47}{}{0}}
\headcommand {\beamer@framepages {47}{47}}
\headcommand {\slideentry {6}{0}{2}{48/52}{}{0}}
\headcommand {\beamer@framepages {48}{52}}
\headcommand {\slideentry {6}{0}{3}{53/53}{}{0}}
\headcommand {\beamer@framepages {53}{53}}
\headcommand {\slideentry {6}{0}{4}{54/54}{}{0}}
\headcommand {\beamer@framepages {54}{54}}
\headcommand {\slideentry {6}{0}{5}{55/55}{}{0}}
\headcommand {\beamer@framepages {55}{55}}
\headcommand {\slideentry {6}{0}{6}{56/56}{}{0}}
\headcommand {\beamer@framepages {56}{56}}
\headcommand {\slideentry {6}{0}{7}{57/57}{}{0}}
\headcommand {\beamer@framepages {57}{57}}
\headcommand {\beamer@partpages {1}{57}}
\headcommand {\beamer@subsectionpages {47}{57}}
\headcommand {\beamer@sectionpages {47}{57}}
\headcommand {\beamer@documentpages {57}}
\headcommand {\gdef \inserttotalframenumber {36}}
