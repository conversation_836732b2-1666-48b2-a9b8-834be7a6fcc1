\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyperFirstAtBeginDocument{\AtBeginDocument}
\HyperFirstAtBeginDocument{\ifx\hyper@anchor\@undefined
\global\let\oldcontentsline\contentsline
\gdef\contentsline#1#2#3#4{\oldcontentsline{#1}{#2}{#3}}
\global\let\oldnewlabel\newlabel
\gdef\newlabel#1#2{\newlabelxx{#1}#2}
\gdef\newlabelxx#1#2#3#4#5#6{\oldnewlabel{#1}{{#2}{#3}}}
\AtEndDocument{\ifx\hyper@anchor\@undefined
\let\contentsline\oldcontentsline
\let\newlabel\oldnewlabel
\fi}
\fi}
\global\let\hyper@last\relax 
\gdef\HyperFirstAtBeginDocument#1{#1}
\providecommand\HyField@AuxAddToFields[1]{}
\providecommand\HyField@AuxAddToCoFields[2]{}
\providecommand \oddpage@label [2]{}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{1}{1/1}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {1}{1}}}
\@writefile{nav}{\headcommand {\slideentry {0}{0}{2}{2/2}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {2}{2}}}
\@writefile{toc}{\beamer@sectionintoc {1}{Background}{3}{0}{1}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {1}{2}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {1}{2}}}
\@writefile{nav}{\headcommand {\sectionentry {1}{Background}{3}{Background}{0}}}
\@writefile{nav}{\headcommand {\slideentry {1}{0}{1}{3/3}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {3}{3}}}
\@writefile{toc}{\beamer@subsectionintoc {1}{1}{Decision Trees}{4}{0}{1}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {3}{3}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{1}{1}{4}{Decision Trees}}}
\providecommand*\caption@xref[2]{\@setref\relax\@undefined{#1}}
\newlabel{fig:DTexample}{{1}{4}{A decision tree and its corresponding decision boundary on a two-dimensional feature space.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:DTexample}{4}}
\@writefile{nav}{\headcommand {\slideentry {1}{1}{1}{4/4}{Decision Trees}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {4}{4}}}
\newlabel{fig:RFexample}{{2}{5}{A decision tree and its corresponding decision boundary on a two-dimensional feature space.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:RFexample}{5}}
\@writefile{nav}{\headcommand {\slideentry {1}{1}{2}{5/5}{Decision Trees}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {5}{5}}}
\@writefile{toc}{\beamer@subsectionintoc {1}{2}{Counterfactual explanations}{6}{0}{1}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {4}{5}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{1}{2}{6}{Counterfactual explanations}}}
\@writefile{nav}{\headcommand {\slideentry {1}{2}{1}{6/6}{Counterfactual explanations}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {6}{6}}}
\newlabel{fig:CFexample}{{3}{7}{Example of the different types of counterfactual explanations (CF) quality (for \(\mu =L_2\)) for the previous tree (Figure~\ref {fig:DTexample}).\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:CFexample}{7}}
\@writefile{nav}{\headcommand {\slideentry {1}{2}{2}{7/7}{Counterfactual explanations}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {7}{7}}}
\@writefile{toc}{\beamer@subsectionintoc {1}{3}{Model extraction attacks}{8}{0}{1}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {6}{7}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{1}{3}{8}{Model extraction attacks}}}
\@writefile{nav}{\headcommand {\slideentry {1}{3}{1}{8/11}{Model extraction attacks}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {8}{11}}}
\@writefile{nav}{\headcommand {\slideentry {1}{3}{2}{12/13}{Model extraction attacks}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {12}{13}}}
\@writefile{nav}{\headcommand {\slideentry {1}{3}{3}{14/15}{Model extraction attacks}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {14}{15}}}
\@writefile{toc}{\beamer@sectionintoc {2}{Project 1 --- From Counterfactuals to Trees: Competitive Analysis of Model Extraction Attacks}{16}{0}{2}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {3}{15}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {8}{15}}}
\@writefile{nav}{\headcommand {\sectionentry {2}{Project 1 --- From Counterfactuals to Trees: Competitive Analysis of Model Extraction Attacks}{16}{Project 1 --- From Counterfactuals to Trees: Competitive Analysis of Model Extraction Attacks}{0}}}
\@writefile{nav}{\headcommand {\slideentry {2}{0}{1}{16/16}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {16}{16}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {16}{16}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{2}{1}{17}{Related Work}}}
\@writefile{nav}{\headcommand {\beamer@subsubsectionentry {0}{2}{1}{1}{17}{Surrogate attacks}}}
\@writefile{nav}{\headcommand {\slideentry {2}{1}{1}{17/19}{Related Work}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {17}{19}}}
\@writefile{nav}{\headcommand {\beamer@subsubsectionentry {0}{2}{1}{2}{20}{Steal-ML}}}
\@writefile{nav}{\headcommand {\slideentry {2}{1}{2}{20/20}{Related Work}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {20}{20}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {17}{20}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{2}{2}{21}{Tree Reconstruction Attack (TRA)}}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces TRA algorithm\relax }}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces TRA algorithm\relax }}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces TRA algorithm\relax }}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces TRA algorithm\relax }}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces TRA algorithm\relax }}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces TRA algorithm\relax }}}
\@writefile{loa}{\contentsline {algocf}{\numberline {1}{\ignorespaces TRA algorithm\relax }}}
\@writefile{nav}{\headcommand {\slideentry {2}{2}{1}{21/27}{Tree Reconstruction Attack (TRA)}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {21}{27}}}
\@writefile{nav}{\headcommand {\slideentry {2}{2}{2}{28/29}{Tree Reconstruction Attack (TRA)}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {28}{29}}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {16}{29}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {21}{29}}}
\@writefile{nav}{\headcommand {\sectionentry {3}{Results}{30}{Results}{0}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {30}{29}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{3}{1}{30}{Theoretical results}}}
\@writefile{nav}{\headcommand {\slideentry {3}{1}{1}{30/31}{Theoretical results}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {30}{31}}}
\@writefile{nav}{\headcommand {\slideentry {3}{1}{2}{32/34}{Theoretical results}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {32}{34}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {30}{34}}}
\@writefile{nav}{\headcommand {\beamer@subsectionentry {0}{3}{2}{35}{Experimental results}}}
\@writefile{nav}{\headcommand {\slideentry {3}{2}{1}{35/35}{Experimental results}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {35}{35}}}
\newlabel{tab:tab_2_results}{{\caption@xref {tab:tab_2_results}{ on input line 522}}{36}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {tab:tab_2_results}{36}}
\@writefile{nav}{\headcommand {\slideentry {3}{2}{2}{36/36}{Experimental results}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {36}{36}}}
\newlabel{fig:sfig1}{{8a}{37}{COMPAS dataset.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:sfig1}{37}}
\newlabel{sub@fig:sfig1}{{a}{37}{COMPAS dataset.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {sub@fig:sfig1}{37}}
\newlabel{fig:sfig2}{{8b}{37}{German Credit dataset.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:sfig2}{37}}
\newlabel{sub@fig:sfig2}{{b}{37}{German Credit dataset.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {sub@fig:sfig2}{37}}
\newlabel{fig:figTRAvsPathFinding}{{8}{37}{Plots of number of queries vs number of nodes for the COMPAS and German Credit datasets.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:figTRAvsPathFinding}{37}}
\@writefile{nav}{\headcommand {\slideentry {3}{2}{3}{37/37}{Experimental results}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {37}{37}}}
\newlabel{fig:2sfig1}{{\caption@xref {fig:2sfig1}{ on input line 562}}{38}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:2sfig1}{38}}
\newlabel{sub@fig:2sfig1}{{}{38}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {sub@fig:2sfig1}{38}}
\newlabel{fig:2sfig2}{{\caption@xref {fig:2sfig2}{ on input line 562}}{38}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:2sfig2}{38}}
\newlabel{sub@fig:2sfig2}{{}{38}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {sub@fig:2sfig2}{38}}
\newlabel{fig:2sfig3}{{\caption@xref {fig:2sfig3}{ on input line 562}}{38}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:2sfig3}{38}}
\newlabel{sub@fig:2sfig3}{{}{38}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {sub@fig:2sfig3}{38}}
\newlabel{fig:2sfig4}{{\caption@xref {fig:2sfig4}{ on input line 562}}{38}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:2sfig4}{38}}
\newlabel{sub@fig:2sfig4}{{}{38}{Experimental results}{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {sub@fig:2sfig4}{38}}
\newlabel{fig:figTRAvsCFandDualCF}{{9}{38}{Plots of means fidelity vs number of nodes.\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:figTRAvsCFandDualCF}{38}}
\@writefile{nav}{\headcommand {\slideentry {3}{2}{4}{38/38}{Experimental results}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {38}{38}}}
\@writefile{toc}{\beamer@sectionintoc {4}{Project 2 --- Learning Voronoi Diagrams for Optimal Counterfactual Explanations in Tree Ensembles}{39}{0}{3}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {30}{38}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {35}{38}}}
\@writefile{nav}{\headcommand {\sectionentry {4}{Project 2 --- Learning Voronoi Diagrams for Optimal Counterfactual Explanations in Tree Ensembles}{39}{Project 2 --- Learning Voronoi Diagrams for Optimal Counterfactual Explanations in Tree Ensembles}{0}}}
\@writefile{nav}{\headcommand {\slideentry {4}{0}{1}{39/39}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {39}{39}}}
\@writefile{nav}{\headcommand {\slideentry {4}{0}{2}{40/40}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {40}{40}}}
\@writefile{nav}{\headcommand {\slideentry {4}{0}{3}{41/41}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {41}{41}}}
\@writefile{nav}{\headcommand {\slideentry {4}{0}{4}{42/42}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {42}{42}}}
\@writefile{nav}{\headcommand {\slideentry {4}{0}{5}{43/43}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {43}{43}}}
\@writefile{toc}{\beamer@sectionintoc {5}{Project 3 --- Guaranteeing Local Optimality in Model-Agnostic Counterfactuals}{44}{0}{4}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {39}{43}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {39}{43}}}
\@writefile{nav}{\headcommand {\sectionentry {5}{Project 3 --- Guaranteeing Local Optimality in Model-Agnostic Counterfactuals}{44}{Project 3 --- Guaranteeing Local Optimality in Model-Agnostic Counterfactuals}{0}}}
\@writefile{nav}{\headcommand {\slideentry {5}{0}{1}{44/44}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {44}{44}}}
\@writefile{nav}{\headcommand {\slideentry {5}{0}{2}{45/45}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {45}{45}}}
\@writefile{nav}{\headcommand {\slideentry {5}{0}{3}{46/46}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {46}{46}}}
\@writefile{toc}{\beamer@sectionintoc {6}{Conclusion and Future works}{47}{0}{5}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {44}{46}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {44}{46}}}
\@writefile{nav}{\headcommand {\sectionentry {6}{Conclusion and Future works}{47}{Conclusion and Future works}{0}}}
\@writefile{nav}{\headcommand {\slideentry {6}{0}{1}{47/47}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {47}{47}}}
\@writefile{nav}{\headcommand {\slideentry {6}{0}{2}{48/52}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {48}{52}}}
\@writefile{nav}{\headcommand {\slideentry {6}{0}{3}{53/53}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {53}{53}}}
\newlabel{fig:Gambs-label}{{10}{54}{Model extraction from counterfactual explanations, Aïvodji et al. 2020\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:Gambs-label}{54}}
\@writefile{nav}{\headcommand {\slideentry {6}{0}{4}{54/54}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {54}{54}}}
\newlabel{fig:Tramer-label}{{11}{55}{Stealing Machine Learning Models via Prediction APIs, Tramer et al. 2016\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:Tramer-label}{55}}
\@writefile{nav}{\headcommand {\slideentry {6}{0}{5}{55/55}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {55}{55}}}
\newlabel{fig:Carlini-label}{{12}{56}{Stealing Part of a Production Language Model, Carlini et al. 2024\relax }{Doc-Start}{}}
\@writefile{snm}{\beamer@slide {fig:Carlini-label}{56}}
\@writefile{nav}{\headcommand {\slideentry {6}{0}{6}{56/56}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {56}{56}}}
\@writefile{nav}{\headcommand {\slideentry {6}{0}{7}{57/57}{}{0}}}
\@writefile{nav}{\headcommand {\beamer@framepages {57}{57}}}
\@writefile{nav}{\headcommand {\beamer@partpages {1}{57}}}
\@writefile{nav}{\headcommand {\beamer@subsectionpages {47}{57}}}
\@writefile{nav}{\headcommand {\beamer@sectionpages {47}{57}}}
\@writefile{nav}{\headcommand {\beamer@documentpages {57}}}
\@writefile{nav}{\headcommand {\gdef \inserttotalframenumber {36}}}
\gdef \@abspage@last{57}
